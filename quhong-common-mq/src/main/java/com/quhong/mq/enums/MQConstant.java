package com.quhong.mq.enums;

public interface MQConstant {
    String TEST_EXCHANGE = "kissu.test.topic";
    String TEST_QUEUE = "kissu.test.queue";
    String TEST_ROUTE_PRE = "kissu.test";
    String TEST_ROUTE = TEST_ROUTE_PRE + ".*";

    /**
     * PK 模块主题 *****************************
     */
    String PK_EXCHANGE = "kissu.mqPk.topic";
    /**
     * pk 开始路由
     */
    String PK_START_ROUTE = "pk.pk_start.route";
    /**
     * pk 结束路由
     */
    String PK_END_ROUTE = "pk.pk_end.route";
    /**
     * user_info 模块主题 *****************************
     */
    String USER_INFO_EXCHANGE = "kissu.mqUserInfo.topic";
    /**
     * 主播质量等级更新路由
     */
    String HOST_QUALITY_LEVEL_UPDATE_ROUTE = "user_info.host_quality_level_update.route";
    /**
     * 主播质量等级更新后续处理队列
     */
    String USER_INFO_HOST_QUALITY_LEVEL_UPDATE_QUEUE = "user_info.host_quality_level_update.queue";
    /**
     * userInfo登录成功处理队列
     */
    String USER_INFO_LOGIN_SUCCESS_QUEUE = "user_info.login_success.queue";

    /**
     * games 模块主题 *****************************
     */
    String GAMES_EXCHANGE = "kissu.mqGames.topic";
    /**
     * 星动嘉年华获得礼物路由
     */
    String STARRY_GET_GIFT_ROUTE = "games.get_gift.route";
    /**
     * user_operation模块主题 *****************************
     */
    String USER_OPERATION_EXCHANGE = "kissu.mqUserOperation.topic";
    /**
     * 关注/取消关注路由
     */
    String FOLLOW_ROUTE = "user_operation.follow.route";

    /**
     * pay模块主题 *****************************
     */
    String PAY_EXCHANGE = "kissu.mqPay.topic";
    /**
     * 首次付费uid路由
     */
    String FIRST_PAYMENT_ROUTE = "first_payment_uid";
    /**
     * 付费uid路由
     */
    String PAYMENT_ROUTE = "payment_uid";

    /**
     * 登录模块主题 *****************************
     */
    String LOGIN_EXCHANGE = "kissu.mqLogin.topic";
    /**
     * 登录注册成功路由密钥
     */
    String LOGIN_REGISTER_SUCCESS_ROUTE = "register_success";
    /**
     * 登录成功路由
     */
    String LOGIN_SUCCESS_ROUTE = "login_success";
    /**
     * 机器人账号生成队列
     */
    String LOGIN_ROBOT_GENERATE_QUEUE = "robot_generate_queue";
    /**
     * 注册判定危险账号标红队列
     */
    String REGISTER_IS_DANGER_ACTOR_QUEUE = "register_is_danger_actor_queue";
    /**
     * 登录判定危险账号标红队列
     */
    String LOGIN_IS_DANGER_ACTOR_QUEUE = "login_is_danger_actor_queue";
    /**
     * 游戏 主题 ************************
     */
    String GAME_EXCHANGE = "kissu.mqGame.topic";
    /**
     * 玩游戏 路由
     */
    String GAME_PLAY_ROUTE = "game_play_route";
    /**
     * 玩游戏处理队列
     */
    String GAME_PLAY_QUEUE = "game.play.queue";
    /**
     * 活动 主题 ********************************
     */
    String ACTIVITY_EXCHANGE = "kissu.mqActivity.topic";
    /**
     * 活动组件化获得活动货币路由
     */
    String EVENT_UNIT_CURRENCY_GET_ROUTE = "event_unit_currency_get_route";
    /**
     * 邀请用户成功路由
     *
     * @see com.quhong.mq.data.activity.InviteUserSuccessMsgData
     */
    String INVITE_USER_SUCCESS_ROUTE = "invite_user_success_route";
    /**
     * 幸运礼物抽奖数据处理路由
     */
    String LUCKY_GIFT_PROCESS_ROUTE = "lucky_gift_process_route";
    /**
     * 活动幸运抽奖数据处理路由
     */
    String EVENT_GAME_LUCKY_DATA_PROCESSING_ROUTE = "event_game_lucky_data_processing_route";
    /**
     * 活动成本计算
     */
    String EVENT_COST_COUNT_ROUTE = "event_cost_count_route";
    /**
     * 用户玩yxsk系列游戏mq 路由
     */
    String YXSK_GAME_PLAY_ROUTE = "yxsk_game_play";
    /**
     * 活动 pk开始处理队列
     * route：PK_START_ROUTE
     */
    String EVENT_PK_START_QUEUE = "activity.pk_start.queue";
    /**
     * 活动 pk结束处理队列
     * route：PK_END_ROUTE
     */
    String EVENT_PK_END_QUEUE = "activity.pk_end.queue";

    /**
     * 活动邀请用户成功处理队列
     * route：INVITE_USER_SUCCESS_ROUTE
     */
    String EVNET_INVITE_USER_SUCCESS_QUEUE = "activity.invite_user_success.queue";
    /**
     * 活动星动嘉年华获得礼物处理队列
     */
    String EVENT_STARRY_GET_GIFT_QUEUE = "activity.starry_get_gift.queue";

    /**
     * 活动幸运奖数据处理队列
     * route：EVENT_LUCKY_GIFT_PROCESS_ROUTE
     * exchange: ACTIVITY_EXCHANGE
     */
    String EVENT_LUCKY_GIFT_PROCESS_QUEUE = "activity.lucky_gift_process.queue";
    /**
     * 玩游戏赢数据处理队列
     * route:MONEY_DETAIL_ROUTE
     * exchange:MONEY_CENTER_EXCHANGE
     */
    String PLAY_GAME_WIN_QUEUE = "activity.play_game_win_queue";
    /**
     * 游戏幸运奖数据处理队列
     */
    String EVENT_GAME_LUCKY_DATA_PROCESSING_QUEUE = "activity.event_game_lucky_data_processing.queue";
    /**
     * 关注/取消关注队列
     * route；FOLLOW_ROUTE
     * exchange: USER_OPERATION_EXCHANGE
     */
    String EVENT_FOLLOW_QUEUE = "activity.follow.queue";
    /**
     * live场次记录队列
     * route: ROOM_CHAPTER_LOG
     * exchange: ROOM_EXCHANGE
     */
    String EVENT_ROOM_CHAPTER_LOG_QUEUE = "activity.room_chapter_log.queue";
    /**
     * 金币流水队列
     */
    String EVENT_MONEY_DETAIL_QUEUE = "activity.event_money_detail.queue";
    /**
     * 游戏幸运奖金币流水队列（金币流水列表）
     */
    String EVENT_GAME_LUCKY_MONEY_DETAIL_QUEUE = "activity.event_game_lucky_money_detail.queue";

    /**
     * 活动模块下麦处理队列
     */
    String EVENT_DOWN_WHEAT_QUEUE = "activity.down_wheat.queue";
    /**
     * 活动成本计算预警
     */
    String EVENT_COST_COUNT_QUEUE = "event_cost_count_queue";
    String ACTIVITY_QUEUE = "kissu.mqActivity.queue";
    /**
     * 幸运person活动队列（处理发礼物）
     */
    String ACTIVITY_LUCKY_USER_QUEUE = "activity.lucky_person.queue";
    /**
     * 发送礼物成功队列
     */
    String ACTIVITY_SEND_GIFT_SUCCESS_QUEUE = "activity.send_gift_success.queue";
    /**
     * 首次付费uid 队列
     */
    String ACTIVITY_FIRST_PAYMENT_QUEUE = "activity.first_payment_uid.queue";
    /**
     * 用户玩yxsk系列游戏队列
     */
    String ACTIVITY_PLAY_YXSK_GAME_QUEUE = "activity.play_yxsk_game.queue";
    /**
     * 用户离开房间mq队列
     */
    String ACTIVITY_LEAVE_ROOM_QUEUE = "activity.leave_room.queue";
    /**
     * 房间内消息mq队列
     */
    String ACTIVITY_ROOM_CHAT_MSG_QUEUE = "activity.room_chat_msg.queue";
    /**
     * 进房mq路由订阅队列
     */
    String ACTIVITY_ENTER_ROOM_MSG_QUEUE = "activity.enter_room.queue";
    /**
     * 付费成功uid队列
     */
    String ACTIVITY_PAYMENT_QUEUE = "activity.payment.queue";
    String ACTIVITY_ROUTE_PRE = "kissu";
    String ACTIVITY_ROUTE = ACTIVITY_ROUTE_PRE + ".*";
    String ACTIVITY_PAY_SUCCESS = "paySuccess";
    String ACTIVITY_PAY_SUCCESS_SUFFIX = "." + ACTIVITY_PAY_SUCCESS;

    String ACTIVITY_SEND_GIFT_SUCCESS = "sendGiftSuccess";
    String ACTIVITY_SEND_GIFT_SUCCESS_SUFFIX = "." + ACTIVITY_SEND_GIFT_SUCCESS;


    /**
     * ROOM 主题 *****************************************
     */
    String ROOM_EXCHANGE = "kissu.mqRoom.topic";

    String ROOM_CHAPTER_LOG = "room_chapter_log";
    /**
     * 房间消息发送，mq
     */
    String ROOM_CHAT_MSG_ROUTE = "room_chat_msg";
    /**
     * 进房mq路由
     */
    String ENTER_ROOM_ROUTE = "enter_room_route";

    /**
     * 离开房间mq密钥(离开房间后)
     */
    String LEAVE_ROOM_ROUTE = "leave_room_route";
    /**
     * 下麦mq密钥
     */
    String DOWN_WHEAT_ROUTE = "down_wheat";
    /**
     * 房主离开直播间mq密钥
     */
    String ANCHOR_LEAVE_ROOM = "anchor_leave_room";
    /**
     * room模块 金币流水处理队列
     */
    String ROOM_MONEY_DETAIL_QUEUE = "room.money_detail.queue";
    /**
     * 房间场次记录队列
     */
    String ROOM_CHAPTER_LOG_QUEUE = "room.room_chapter_log.queue";
    /**
     * 关注/取消关注处理队列
     */
    String ROOM_FOLLOW_QUEUE = "room.follow.queue";
    /**
     * 房主离开直播间更新直播开播任务
     */
    String ANCHOR_OPEN_LIVE_TASK = "anchor_open_live_task";
    /**
     * 用户进房mq处理队列
     */
    String ROOM_ENTER_ROOM_QUEUE = "room.real_enter_room.queue";

    /**
     * 用户离开房间统计队列
     */
    String ROOM_LEAVE_ROOM_QUEUE = "room.enter_room.queue";
    /**
     * 上麦队列
     */
    String ROOM_DOWN_WHEAT_QUEUE = "room.down_wheat.queue";
    /**
     * 语聊房送礼成功队列
     */
    String ROOM_CHAT_ROOM_SEND_GIFT_SUCCESS_QUEUE = "room.chat_room_send_gift_success.queue";
    /**
     * 送礼成功队列（主播有收益才会发送）
     */
    String ROOM_SEND_GIFT_SUCCESS_QUEUE = "room.send_gift_success.queue";

    String ROOM_ANCHOR_LEAVE_ROOM_QUEUE = "room.anchor_leave_room.queue";

    String ROOM_ANCHOR_OPEN_LIVE_TASK_QUEUE = "room.anchor_open_live_task.queue";
    String ROOM_ANCHOR_OPEN_LIVE_TASK_PERFORMANCE_QUEUE = "room.anchor_open_live_task.queue";

    String HOST_GUARD_OPEN_QUEUE = "host_guard_open_queue";
    String HOST_GUARD_OPEN_ROUTE = "host_guard_open_route";


    /**
     * Gift 主题******************************************
     */
    String GIFT_EXCHANGE = "kissu.mqGift.topic";
    /**
     * 礼物发送成功mq密钥
     */
    String SEND_GIFT_SUCCESS_ROUTE = "send_gift_success";
    /**
     * 语聊房礼物发送成功密钥
     */
    String CHAT_ROOM_SEND_GIFT_SUCCESS_ROUTE = "chat_room_send_gift_success";
    /**
     * 主播开播任务业绩更新
     */
    String SEND_ROOM_GIFT_HOST_OPEN_LIVE_TASK = "SEND_ROOM_GIFT_HOST_OPEN_LIVE_TASK";


    /**
     * user_auth 主题 **************************
     */
    String USER_AUTH_EXCHANGE = "kissu.mqUserAuth.topic";
    /**
     * user auth 接收金币变动信息队列
     */
    String USER_AUTH_MONEY_DETAIL_QUEUE = "user_auth.money_detail.queue";

    /**
     * money_center 主题 *******************
     */
    String MONEY_CENTER_EXCHANGE = "kissu.mqMoneyCenter.topic";
    /**
     * 金币变动信息路由
     */
    String MONEY_DETAIL_ROUTE = "money_detail_record";

    /**
     * 勋章下发路由
     */
    String MEDAL_ISSUE_ROUTE = "medal_issue";

    /**
     * 勋章下发 交换器
     */
    String MEDAL_ISSUE_EXCHANGE = "kissu.mqMedal.topic";


    /**
     * user auth 勋章下发队列
     */
    String USER_AUTH_ISSUE_MEDAL_QUEUE = "user_auth.issue_medal.queue";

    String RANK_EXCHANGE = "kissu.mqRank.topic";
    /**
     * rank模块money_detail处理队列
     */
    String RANK_MONEY_DETAIL_QUEUE = "rank_money_detail_queue";
    String RANK_QUEUE = "kissu.mqRank.queue";
    String RANK_ROUTE_PRE = "kissu.mqRank";
    String RANK_ROUTE = RANK_ROUTE_PRE + ".*";
    String LIVE_RANK_CONTRIBUTE_SUFFIX_CONNECTOR = ".";
    String LIVE_RANK_CONTRIBUTE_SUFFIX_KEY = "liveRank";
    String GIFT_RANK = "giftRank";
    String RECHARGE_RANK = "rechargeRank";
    String CACHE_CHANGE_GOLD = "cache_change_gold";

    String RANK_GIFT = "kissu.mqRank.gift";

    String RANK_GIFT_ROUTE = "kissu.rank.gift_route";
    String RANK_GIFT_EXCHANGE = "kissu.rank.gift_exchange";
    String RANK_GIFT_QUEUE = "kissu.mqRank.gift.queue";

    String LIVE_RANK_CONTRIBUTE_SUFFIX = LIVE_RANK_CONTRIBUTE_SUFFIX_CONNECTOR + LIVE_RANK_CONTRIBUTE_SUFFIX_KEY;
    String SEND_MESSAGE_EXCHANGE = "kissu.mqMessage.topic";
    String SEND_MESSAGE_QUEUE = "kissu.mqMessage.queue";
    String SEND_MESSAGE_ROUTE_PRE = "kissu";
    String SEND_MESSAGE_ROUTE = SEND_MESSAGE_ROUTE_PRE + ".*";
    String SAVE_MESSAGE_SUFFIX = ".save_msg"; //礼物消息保存 mq消息后缀

    String NOTIFICATION_EXCHANGE = "kissu.mqNotification.topic";
    String NOTIFICATION_QUEUE = "kissu.mqNotification.queue";
    String NOTIFICATION_ROUTE_PRE = "kissu.mqNotification";
    String NOTIFICATION_ROUTE = NOTIFICATION_ROUTE_PRE + ".*";

    String PK_RANK_ROUTE = "pk_rank_route";
    String PK_RANK_QUEUE = "pk_rank_queue";

    String LIVE_ROOM_GIFT_TASK_EXCHANGE = "py_exchange";
    String LIVE_ROOM_GIFT_TASK_QUEUE = "all_task_queue";
    String LIVE_ROOM_GIFT_TASK_ROUTE_PRE = "all_task";
    String LIVE_ROOM_GIFT_TASK_ROUTE = LIVE_ROOM_GIFT_TASK_ROUTE_PRE + ".*";
    String LIVE_ROOM_GIFT_GAIN_SUFFIX = ".live_room_gift_gain"; //房间礼物任务mq消息后缀

    String LIVE_SHARE_TIMES_TASK_EXCHANGE = "py_exchange";
    String LIVE_SHARE_TIMES_TASK_QUEUE = "all_task_queue";
    String LIVE_SHARE_TIMES_TASK_ROUTE_PRE = "all_task";
    String LIVE_SHARE_TIMES_TASK_ROUTE = LIVE_SHARE_TIMES_TASK_ROUTE_PRE + ".*";
    String LIVE_SHARE_TIMES_TASK_SUFFIX = ".live_room_share"; //房间礼物任务mq消息后缀

    String FIRST_FOLLOW_COUNT_TASK_EXCHANGE = "py_exchange";
    String FIRST_FOLLOW_COUNT_TASK_QUEUE = "all_task_queue";
    String FIRST_FOLLOW_COUNT_TASK_ROUTE_PRE = "all_task";
    String FIRST_FOLLOW_COUNT_TASK_ROUTE = FIRST_FOLLOW_COUNT_TASK_ROUTE_PRE + ".*";
    String FIRST_FOLLOW_COUNT_TASK_SUFFIX = ".first_follow_count"; //首次关注数量

    String HOST_TO_NEW_USER_MSG_TASK_EXCHANGE = "py_exchange";
    String HOST_TO_NEW_USER_MSG_TASK_QUEUE = "all_task_queue";
    String HOST_TO_NEW_USER_MSG_TASK_ROUTE_PRE = "all_task";
    String HOST_TO_NEW_USER_MSG__TASK_ROUTE = HOST_TO_NEW_USER_MSG_TASK_ROUTE_PRE + ".*";
    String HOST_TO_NEW_USER_MSG_TASK_SUFFIX = ".host_to_new_user_msg"; //主播打招呼任务


    String ROOM_SEND_MSG_TASK_EXCHANGE = "py_exchange";
    String ROOM_SEND_MSG_TASK_QUEUE = "all_task_queue";
    String ROOM_SEND_MSG_TASK_ROUTE_PRE = "all_task";
    String ROOM_SEND_MSG_TASK_ROUTE = ROOM_SEND_MSG_TASK_ROUTE_PRE + ".*";
    String FIRST_FOLLOW_COUNT_SUFFIX = ".room_send_msg"; //房间里面发消息任务

    String ONCE_TASK_EXCHANGE = "py_exchange";
    String ONCE_TASK_QUEUE = "all_task_queue";
    String ONCE_TASK_ROUTE_PRE = "all_task";
    String ONCE_TASK_ROUTE = ONCE_TASK_ROUTE_PRE + ".*";
    String ONCE_GAIN_SUFFIX = ".once_task_complete_info"; //房间礼物任务mq消息后缀

    String ROUTE_ROOM_USER_CONTRIBUTE_EXCHANGE = "py_exchange";
    String ROUTE_ROOM_USER_CONTRIBUTE_QUEUE = "py_queue";
    String ROUTE_ROOM_USER_CONTRIBUTE_ROUTE_PRE = "kissu";
    String ROUTE_ROOM_USER_CONTRIBUTE_ROUTE = ROUTE_ROOM_USER_CONTRIBUTE_ROUTE_PRE + ".*";
    String ROUTE_ROOM_USER_CONTRIBUTE_SUFFIX = ".route_room_user_contribute"; //房间礼物贡献值处理 mq消息后缀

    String ROUTE_EVALUATE_TASK_EXCHANGE = "py_exchange";
    String ROUTE_EVALUATE_TASK_QUEUE = "py_queue";
    String ROUTE_EVALUATE_TASK_PRE = "kissu";
    String ROUTE_EVALUATE_TASK_ROUTE = ROUTE_EVALUATE_TASK_PRE + ".*";
    String ROUTE_EVALUATE_TASK_SUFFIX = ".get_evaluate"; //好评任务 mq消息后缀

    String ROUTE_EFFECTIVE_MIC_USER_TASK_EXCHANGE = "py_exchange";
    String ROUTE_EFFECTIVE_MIC_USER_TASK_QUEUE = "py_queue";
    String ROUTE_EFFECTIVE_MIC_USER_TASK_PRE = "kissu";
    String ROUTE_EFFECTIVE_MIC_USER_TASK_ROUTE = ROUTE_EFFECTIVE_MIC_USER_TASK_PRE + ".*";
    String ROUTE_EFFECTIVE_MIC_USER_TASK_SUFFIX = ".effective_mic_user"; //热聊用户数用户 上麦活跃 mq消息后缀

    String ROUTE_USER_STAY_IN_ROOM_TASK_EXCHANGE = "py_exchange";
    String ROUTE_USER_STAY_IN_ROOM_TASK_QUEUE = "py_queue";
    String ROUTE_USER_STAY_IN_ROOM_TASK_PRE = "kissu";
    String ROUTE_USER_STAY_IN_ROOM_TASK_ROUTE = ROUTE_USER_STAY_IN_ROOM_TASK_PRE + ".*";
    String ROUTE_USER_STAY_IN_ROOM_TASK_SUFFIX = ".user_stay_in_room"; //用户在语聊房呆的时间


    //***************** detect 鉴黄主题
    String DETECT_EXCHANGE = "kissu.mqDetect.topic";
    /**
     * 头像鉴黄处理路由
     */
    String DETECT_ACTOR_HEAD_ROUTE = "detect_actor_head_route";
    /**
     * 头像鉴黄处理队列
     */
    String DETECT_ACTOR_HEAD_QUEUE = "detect_actor_head_queue";

    // 主播开播任务业绩
    String HOST_OPEN_LIVE_TASK_EXCHANGE = "kissu.mqHostOpenLiveTask.topic";
    String HOST_OPEN_LIVE_PERFORMANCE_CHANGE_ROUTE = "host_open_live_performance_change_route";
    String HOST_OPEN_LIVE_PERFORMANCE_CHANGE_QUEUE = "host_open_live_performance_change_queue";


    //******************* agent_center 主题
    String ALL_TASK_VIDEO_CALL_END_ROUTE = "all_task.video_call_end";
    String AGENT_CENTER_EXCHANGE = "kissu.mqAgentCenter.topic";
    String AGENT_PERFORMANCE_CHANGE_ROUTE = "agent_performance_change_route";
    /**
     * 主播换绑，解绑，绑定 代理 路由
     */
    String AGENT_CENTER_HOST_REBINDING = "agent_center.host_rebinding.route";
    /**
     * 代理换绑，解绑，绑定 代理 路由
     */
    String AGENT_CENTER_AGENT_REBINDING = "agent_center.agent_rebinding.route";
    /**
     * 代理主账号换绑，解绑，绑定 uid 路由
     */
    String AGENT_CENTER_AGENT_MASTER_REBINDING = "agent_center.agent_master_rebinding.route";
    String AGENT_PERFORMANCE_CHANGE_QUEUE = "agent_performance_change_queue";
    String AGENT_BLOCK_OPERATE_ROUTE = "agent_block_operate_route";
    String AGENT_BLOCK_OPERATE_QUEUE = "agent_block_operate_queue";
    /**
     * agent-center金币流水mq队列
     */
    String AGENT_CENTER_MONEY_DETAIL_QUEUE = "agent_center.money_detail.queue";
    /**
     * agent-center用户离开房间mq队列
     */
    String AGENT_CENTER_LEAVE_ROOM_QUEUE = "agent_center.leave_room.queue";
    /**
     * agent-center模块下麦处理队列
     */
    String AGENT_CENTER_DOWN_WHEAT_QUEUE = "agent_center.down_wheat.queue";

    /**
     * agent-center模块送礼处理队列
     */
    String AGENT_CENTER_SEND_GIFT_SUCCESS_QUEUE = "agent_center.send_gift_success.queue";
    /**
     * agent-center模块通话时长处理队列
     */
    String AGENT_CENTER_CHAT_TIME_QUEUE = "agent_center.chat_time.queue";

    /**
     * 主播换绑，解绑，绑定 代理 处理队列
     */
    String AGENT_CENTER_HOST_REBINDING_QUEUE = "agent_center.host_rebinding.queue";
    /**
     * 代理换绑，解绑，绑定 代理 处理队列
     */
    String AGENT_CENTER_AGENT_REBINDING_QUEUE = "agent_center.agent_rebinding.queue";
    /**
     * 代理主账号换绑，解绑，绑定 uid 处理队列
     */
    String AGENT_CENTER_AGENT_MASTER_REBINDING_QUEUE = "agent_center.agent_master_rebinding.queue";
    /**
     * agent-center房间场次日志处理队列
     * route: ROOM_CHAPTER_LOG
     * exchange: ROOM_EXCHANGE
     */
    String AGENT_CENTER_ROOM_CHAPTER_LOG_QUEUE = "agent_center.room_chapter_log.queue";

    //****************invite
    String INVITE_EXCHANGE = "kissu.mqInvite.topic";
    String INVITE_DIVIDE_INTO_ROUTE = "invite_divide_into_route";
    String INVITE_DIVIDE_INTO_QUEUE = "invite_divide_into_queue";

    // auto-message
    String AUTO_MESSAGE_EXCHANGE = "kissu.autoMessage.topic";
    String AUTO_MESSAGE_QUEUE = "auto_message_queue";
    String AUTO_MESSAGE_PRE = "auto_message";
    String AUTO_MESSAGE_ROUTE = AUTO_MESSAGE_PRE + ".*";


    // auto-message
    String TEMPLATE_TASK_EXCHANGE = "kissu.templateTask.topic";
    String TEMPLATE_TASK_QUEUE = "template_task_queue";
    String TEMPLATE_TASK_PRE = "template_task";
    String TEMPLATE_TASK_ROUTE = TEMPLATE_TASK_PRE + ".*";

    //doris
    String DORIS_TOOLS_EXCHANGE = "kissu.mqDorisTools.topic";
    String DORIS_TOOLS_MONEY_DETAIL_QUEUE = "doris_tools_money_detail_queue";
    String DORIS_TOOLS_MONEY_DETAIL_ROUTE = "doris_tools_money_detail_route";

    String DORIS_TOOLS_AGENT_PERFORMANCE_QUEUE = "doris_tools_agent_performance_queue";
    String DORIS_TOOLS_AGENT_PERFORMANCE_ROUTE = "doris_tools_agent_performance_route";

    //chat***************************************************************************
    String CHAT_EXCHANGE = "kissu.chat.topic";

    /**
     * 结束通话 处理路由
     */
    String END_CHAT_PROCESS_ROUTE = "end_chat_by_system.queue";
    /**
     * 结束通话 处理队列
     */
    String END_CHAT_PROCESS_QUEUE = "chat.end_chat_by_system.queue";
    //chat end*************************************************************************
}
