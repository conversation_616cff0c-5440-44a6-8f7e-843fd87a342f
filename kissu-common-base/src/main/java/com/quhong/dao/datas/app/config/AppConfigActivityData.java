package com.quhong.dao.datas.app.config;

import com.quhong.constant.GiftFromTypeConstant;
import com.quhong.constant.activity.EventGroupConstant;
import com.quhong.dao.datas.BaseOperationData;
import com.quhong.dao.datas.app.config.activity.model.EventModeInfo;
import com.quhong.dao.datas.app.config.activity.model.JoinModeInfo;
import com.quhong.dao.datas.app.config.activity.model.page.ModelPageInfo;
import com.quhong.dao.datas.app.config.activity.unit.EventUnit;
import com.quhong.dao.datas.handler.impl.activity.event.unit.EventUnitArrayTypeHandler;
import com.quhong.dao.datas.handler.impl.activity.model.join.mode.EventModeInfoTypeHandler;
import com.quhong.dao.datas.handler.impl.activity.model.join.mode.JoinModeInfoTypeHandler;
import com.quhong.dao.datas.handler.impl.activity.model.join.mode.PageInfoTypeHandler;
import com.quhong.data.mysql.handler.JoinStrToIntSetTypeHandler;
import com.quhong.data.mysql.handler.JoinStrToStrSetTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import tk.mybatis.mapper.annotation.ColumnType;

import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Set;

/**
 * 活动配置 实体类（app_config_activity）
 *
 * <AUTHOR>
 * @date 2022-12-05  14:06
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "app_config_activity")
public class AppConfigActivityData extends BaseOperationData {
    @Id
    @GeneratedValue(generator = "JDBC")
    private Integer id;
    /**
     * 活动code
     */
    private Integer activityCode;
    /**
     * 事件分组 1:PK活动 2活动模板活动 3组件化活动
     * @see EventGroupConstant 活动分组
     */
    private Integer eventGroup;
    /**
     * 名字
     */
    private String name;
    /**
     * 描述
     */
    private String activityDesc;
    /**
     * 活动使用时区
     * @see com.quhong.constant.activity.model.event.mode.ZoneOffsetConstant 时区偏移常量
     */
    private String zoneOffset;
    /**
     * 活动格式化日期格式
     * @see com.quhong.constant.activity.model.event.mode.DateFormatPatternConstant 日期格式常量
     */
    private String dateFormat;
    /**
     * 活动开始时间
     */
    private Long startTime;
    /**
     * 活动结束时间
     */
    private Long endTime;
    /**
     * 金币赠送 操作类型
     */
    private Integer actType;
    /**
     * 物品赠送 场景id
     */
    private Integer itemSceneId;
    /**
     * 渠道列表（为空则不限制）
     */
    @ColumnType(typeHandler = JoinStrToStrSetTypeHandler.class)
    private Set<String> channelSet;
    /**
     * 国家列表（为空则不限制）
     */
    @ColumnType(typeHandler = JoinStrToStrSetTypeHandler.class)
    private Set<String> countryCodeSet;
    /**
     * 活动链接
     */
    private String url;
    /**
     * 参与方式信息(参与活动条件)
     */
    @ColumnType(typeHandler = JoinModeInfoTypeHandler.class)
    private JoinModeInfo joinModeInfo;
    /**
     * 禁止参与用户rid列表
     */
    @ColumnType(typeHandler = JoinStrToIntSetTypeHandler.class)
    private Set<Integer> notJoinRidSet;
    /**
     * 类别
     * @see com.quhong.constant.activity.EventCategoryConstant  活动类别
     */
    private Integer category;
    // 以下为活动组件化额外信息
    /**
     * 活动组件列表
     */
    @ColumnType(typeHandler = EventUnitArrayTypeHandler.class)
    private List<EventUnit> units;
    /**
     * 通知图片
     */
    private String noticeImg;
    /**
     * 奖励key列表
     */
    @ColumnType(typeHandler = JoinStrToStrSetTypeHandler.class)
    private Set<String> awardsKeys;


    // 以下为活动模板额外信息

    /**
     * 活动方式信息
     */
    @ColumnType(typeHandler = EventModeInfoTypeHandler.class)
    private EventModeInfo eventModeInfo;
    /**
     * 榜单类型
     *
     * @see com.quhong.core.annotation.ActivityRankType 活动榜单类型
     */
    @ColumnType(typeHandler = JoinStrToIntSetTypeHandler.class)
    private Set<Integer> rankType;
    /**
     * 页面信息
     */
    @ColumnType(typeHandler = PageInfoTypeHandler.class)
    private ModelPageInfo pageInfo;


    // 以下为旧活动模版逻辑兼容字段（新活动模版不再使用）
    /**
     * 礼物榜单则需要填写礼物id ,分割
     *
     * @see com.quhong.dao.datas.GiftListConfigData 礼物配置类.giftId
     */
    @ColumnType(typeHandler = JoinStrToIntSetTypeHandler.class)
    private Set<Integer> dataId;
    /**
     * 礼物榜单需要填写礼物来源场景
     *
     * @see GiftFromTypeConstant 礼物来源类型
     */
    private Integer fromType;
    /**
     * 渠道
     */
    private String channel;
    /**
     * 用户榜取数据top
     */
    private Integer userDataTop;
    /**
     * 主播榜取数据top
     */
    private Integer hostDataTop;

    /**
     * 用户实时榜单展示条数
     */
    private Integer userShowTop;
    /**
     * 主播实时榜单展示条数
     */
    private Integer hostShowTop;
    /**
     * 用户榜单奖励人数
     */
    private Integer userAwardTop;
    /**
     * 主播榜单奖励人数
     */
    private Integer hostAwardTop;
    /**
     * 面向用户国家列表(,分割存储)
     */
    @ColumnType(typeHandler = JoinStrToStrSetTypeHandler.class)
    private Set<String> userCountryGroupStr;
    /**
     * 面向主播国家列表（,分割存储）
     */
    @ColumnType(typeHandler = JoinStrToStrSetTypeHandler.class)
    private Set<String> hostCountryGroupStr;
    /**
     * 用户榜单查数据使用actType列表（,分割存储）
     */
    @ColumnType(typeHandler = JoinStrToIntSetTypeHandler.class)
    private Set<Integer> userActionType;
    /**
     * 主播榜单查数据使用actType列表（,分割存储）
     */
    @ColumnType(typeHandler = JoinStrToIntSetTypeHandler.class)
    private Set<Integer> hostActionType;
    /**
     * 用户榜单查询数据 segCode列表  ,分割
     */
    @ColumnType(typeHandler = JoinStrToIntSetTypeHandler.class)
    private Set<Integer> userSegCode;
    /**
     * 主播榜单查询数据 segCode列表  ,分割
     */
    @ColumnType(typeHandler = JoinStrToIntSetTypeHandler.class)
    private Set<Integer> hostSegCode;
    /**
     * 1增加 2减少
     */
    private Integer userAction;
    private Integer hostAction;
    /**
     * 1金币 2 钻石
     */
    private Integer userCurrency;
    private Integer hostCurrency;

    private Long mtime;

    private String operator;

    public String findCurrDateStr( long currTime) {
        ZoneId zoneId = ZoneOffset.of(this.getZoneOffset());
        return Instant.ofEpochSecond(currTime)
                .atZone(zoneId)
                .format(DateTimeFormatter.ofPattern(this.getDateFormat()));
    }

    public interface validType{
        int OFF_LINE = 0;
        int SERVING = 1;
    }



    //************* 其他参数
//    @Transient
//    private Set<Integer> dataIdSet;
//    @Transient
//    private Set<String> userCountryCodeSet;
//    @Transient
//    private Set<String> hostCountryCodeSet;
//    @Transient
//    private Set<Integer> userActTypeSet;
//    @Transient
//    private Set<Integer> hostActTypeSet;

//    public Set<Integer> generateAndGetDataIdSet(){
//        if (ObjectUtils.isEmpty(this.dataIdSet)) {
//            this.dataIdSet = StringUtils.getIdSetToIdStr(this.dataId, ",");
//        }
//        return this.dataIdSet;
//    }
//
//    public Set<String> generateAndGetUserCountryCodeSet(){
//        if (ObjectUtils.isEmpty(this.userCountryCodeSet)) {
//            this.userCountryCodeSet = StringUtils.getStrSetFromStr(this.userCountryGroupStr, ",");
//        }
//        return this.userCountryCodeSet;
//    }
//
//    public Set<String> generateAndGetHostCountryCodeSet(){
//        if (ObjectUtils.isEmpty(this.hostCountryCodeSet)) {
//            this.userCountryCodeSet = StringUtils.getStrSetFromStr(this.hostCountryGroupStr, ",");
//        }
//        return this.userCountryCodeSet;
//    }
//
//    public Set<Integer> createAndGetUserActTypeSet(){
//        if (ObjectUtils.isEmpty(this.userActTypeSet)) {
//            this.userActTypeSet = StringUtils.getIdSetToIdStr(this.userActionType, ",");
//        }
//        return this.userActTypeSet;
//    }
//
//    public Set<Integer> createAndGetHostActTypeSet(){
//        if (ObjectUtils.isEmpty(this.hostActTypeSet)) {
//            this.hostActTypeSet = StringUtils.getIdSetToIdStr(this.hostActionType, ",");
//        }
//        return this.hostActTypeSet;
//    }
}
