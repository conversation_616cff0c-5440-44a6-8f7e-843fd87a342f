package com.quhong.enums;

import com.quhong.constant.DurationTypeConstant;
import com.quhong.constant.items.ItemSceneIdConstant;
import lombok.Getter;

/**
 * Description: 活动类型Enum
 *
 * <AUTHOR>
 * @date 2021/12/9 10:59
 */
@Getter
public enum ActivityTypeEnum {
    DEFAULT(0, "Activity", "默认值", 0),
    CHECK_IN(1, "Check In", "签到", ItemSceneIdConstant.CHECK_IN_GAIN),
    TIME_BOX(2, "Time Box", "时长宝箱", ItemSceneIdConstant.ROOM_ONLINE_GET_GAIN),
    SMASH_EGG(3, "Smash eggs", "砸蛋", ItemSceneIdConstant.EGG_GET_GAIN, true, 5, true, true, true),
    LUCKY_GIFT(9, "Lucky Gift", "幸运礼物", 0, false),
    LIVE_ROOM_ROSE_RACE(4, "Rose Gift Competition", "玫瑰花比赛活动", ItemSceneIdConstant.LIVE_ROSE_RACE_GAIN),
    AD_REWARD(5, "Advertising Rewards", "广告奖励", ItemSceneIdConstant.AD_REWARD_GAIN),
    OPEN_NOTIFICATION_TASK(6, "Open Notification Task", "打开通知任务", ItemSceneIdConstant.OPEN_NOTIFICATION_GAIN),
    DAILY_RECHARGE_CAROUSEL(7, "Daily First Recharge Carousel", "每日首充转盘", ItemSceneIdConstant.DAILY_RECHARGE_CAROUSEL_GAIN),
    LIVE_BEATING_HEART_RACE(8, "Beating Heart Activity", "live跳动的心活动", ItemSceneIdConstant.LIVE_BEATING_HEART_RACE_GAIN),
    REINA_OF_LATIN_AMERICA(10, "Reina Of Latin America", "拉美女王选美大赛", ItemSceneIdConstant.REINA_OF_LATIN_AMERICA_GAIN),
    GOODS_LIST(11, "goods list", "for goods list", ItemSceneIdConstant.CHARGE_REWARD_GAIN),
    FIRST_CHARGE_REWARD_LIST(26, "first charge reward list", "for first charge", ItemSceneIdConstant.FIRST_CHARGE_REWARD),
    LUCKY_NUMBER(12, "Lucky Number", "老虎机", 0, true, 5, true, true, true),
    RING_GIFT_COMPETITION(13, "Ring Gift Competition", "钻戒礼物活动比赛", ItemSceneIdConstant.RING_GIFT_COMPETITION_GAIN),
    HALLOWEEN_ACTIVITY(14, "Halloween Activity", "万圣节活动", ItemSceneIdConstant.HALLOWEEN_ACTIVITY),
    KISS_GIFT_COMPETITION(15, "Kiss Gift Competition", "Kiss礼物活动比赛", ItemSceneIdConstant.KISS_ACTIVITY),
    THANKS_GIVING_ACTIVITY(16, "Happy Thanksgiving", "感恩节活动", ItemSceneIdConstant.THANKS_GIVING_ACTIVITY_GAIN),

    CHRISTMAS_ACTIVITY(17, "Christmas", "圣诞节活动", ItemSceneIdConstant.CHRISTMAS_ACTIVITY_GAIN),

    WEEK_STAR_ACTIVITY(18, "Week Star", "旧周星活动", ItemSceneIdConstant.WEEK_STAR_ACTIVITY_GAIN),

    SEND_LOVER_IPHONE(19, "Send your lover an iPhone", "送主播Iphone活动", 0),

    LUCKY_PERSON_ACTIVITY(20, "Who's the lucky person", "Lucky Person活动", 0),

    INDEPENDENCE_DAY_ACTIVITY(21, "Happy independence day", "独立日活动", ItemSceneIdConstant.INDEPENDENCE_DAY_ACTIVITY_GAIN),

    BIG_CASH_PRIZE_COMING(22, "Big Cash Prize Coming", "现金大奖活动", 0),

    GIFT_DISCOUNT_ACTIVITY(23, "Giving gift ranking", "礼物折扣活动", 0),

    SUPER_GREEDY_CAT_ACTIVITY(24, "Super Greedy", "Greedy cat游戏活动", 0),

    WEB_INVITE_EFFECTIVE_USER_REWARD(25, "Invite Effective User", "WEB邀请有效用户奖励", 0),

    HOTCHAT_CHAT_ROOM_ACTIVITY(26, "Let's party", "Hotchat 语聊活动", ItemSceneIdConstant.HOTCHAT_CHAT_ROOM_ACTIVITY_GAIN),
    HOTCHAT_GAME_RANK(27, "Game Star", "Hotchat游戏消费排行榜", 0),
    TIKKO_GAME_RANK(28, "Game Star", "Tikko游戏消费排行榜", 0),
    HOTCHAT_ROOM_RECEIVE_GIFT_RANK(29, "Hall of Honor", "Hotchat礼物收礼排行榜", 0),
    HOTCHAT_ROOM_SEND_GIFT_RANK(30, "Hall of Honor", "Hotchat礼物送礼排行榜", 0),
    TIKKO_AGENT_SON_COMMISSION_RANK(31, "Popular Agency", "Tikko子代理业绩榜", 0),
    TIKKO_INVITE_RECHARGE_RANK(32, "Invite recharge Rank", "Tikko邀请充值榜", 0),
    TIKKO_LONG_TERM_PK(33, "PK Bonus Battle", "Tikko长期pk活动", 0, 502625,0, DurationTypeConstant.CURR_WEEK),
    TIKKO_LONG_TERM_INVITE_RANK(34, "Invite Reward", "Tikko邀请奖励活动", 0, 125000,125000, DurationTypeConstant.CURR_WEEK),

    RANK_REWARD(50, "Rank Reward", "排行榜奖励", 0),
    SAY_HI_REPLY_REWARD(57, "", "sayHi回复奖励", 0),
    GEAR_REWARD(58, "gear reward", "", 0),
    ROOM_ROCKET_REWARD(59, "room rocket reward", "", 0),
    FEEDBACK_REWARD(60, "Feedback Reward", "反馈奖励", 0),


    //游戏id 从99开始
    HKYS_GAME_HORSE_RACING(98, "Horse racing", "赛马游戏", 0, true, 5, true, true, true),
    HKYS_GAME_ROLL_THE_DICE(99, "Play dice", "hkys 游戏 掷骰子", 0, true, 5, true, true, true),
    JOY_GAME(100, "games lobby", "", 0, true, 5, true, true, true),

    JOY_CAR_RACING(101, "Car racing", "赛车游戏", 0, true, 17000, true, true, true),

    YXSK_TEEN_PATTI(102, "Teen Patti", "Teen Patti", 0, true, 5, true, true, true),
    YXSK_GREEDY_CAT(103, "Greedy Cat", "Greedy Cat", 0, true, 5, true, true, true),
    YXSK_GUESSING_FISTS(104, "Guessing fists", "Guessing fists", 0, true, 5, true, true, true),
    YXSK_LUDO(105, "Ludo", "Ludo", 0, true, 5, true, true, true),
    YXSK_TURNTABLE(106, "Turntable", "Turntable", 0, true, 5, true, true, true),
    JOY_SLOT(107, "Slot", "Joy老虎机", 0, true, 5, true, true, true),
    JOY_FRUIT_MACHINE(108, "Fruit Machine", "Joy 水果摩天轮", 0, true, 5, true, true, true),
    JOY_BLACK_JACK(109, "Blackjack", "Joy blackjack", 0, true, 5, true, true, true),
    JOY_GREEDY(113, "Greedy Lion", "Joy Greedy", 0, true, 5, true, true, true),
    JOY_FISH(114, "Amazing Fishing", "Joy Fish", 0, true, 5, true, true, true),
    BAISUN_SLOTS(110, "Slots", "百顺 Slots", 0, true, 5, true, true, true),
    BAISUN_GREEDY(111, "Greedy", "百顺 greedy", 0, true, 5, true, true, true),
    BAISUN_FISHING_STAR(112, "Fishing Star", "百顺 Fishing Star", 0, true, 5, true, true, true),
    BAISUN_CRASH_PRO(115, "Crash", "百顺 Crash", 0, true, 5, true, true, true),
    SELF_GREED(116, "Greedy Elephant", "自研 Greed", 0, true, 5, true, true, true),
    SELF_BATTLE(117, "Big Battle", "自研 Battle", 0, true, 5, true, true, true),
    SELF_YUMMY_SLOT(118, "Yummy Slot old", "自研 Yummy Slot", 0, true, 5, true, true, true),
    SELF_TEENPATTI(119, "Green Teenpatti", "自研 Teenpatti", 0, true, 5, true, true, true),
    SELF_YUMMY_NINE(120, "Yummy Slot", "自研 Yummy Nine", 0, true, 5, true, true, true),
    SELF_ANIMAL_PARTY(121, "Forest Party", "自研 Animal Party", 0, true, 5, true, true, true),
    SELF_ROYAL_BATTLE(122, "Royal Battle", "自研 Royal Battle", 0, true, 5, true, true, true),
    SELF_PENALTY_SHOOTOUT(123, "Penalty Shootout", "点球大战", 0, true, 5, true, true, true),
    BAISUN_LUDO(124, "LUDO", "百顺 LUDO", 0, true, 5, true, true, true),
    BAISUN_JELLY_BOOM(125, "JellyBoom", "百顺 JellyBoom", 0, true, 5, true, true, true),
    BAISUN_UNO(126, "UNO", "百顺 UNO", 0, true, 5, true, true, true),
    BAISUN_BALOOT(127, "Baloot", "百顺 Baloot", 0, true, 5, true, true, true),
    BAISUN_SNAKE_LADDER(128, "SnakeLadder", "百顺 SnakeLadder", 0, true, 5, true, true, true),
    SELF_FISH(129, "Lucky Fishing", "自研 Lucky Fish", 0, true, 5, true, true, true),
    SELF_ALADDIN_SLOT_APP_NAME(130, "aladdin slot", "自研 阿拉丁神灯 aladdin slot", 0, true, 5, true, true, true),
    SELF_ROULETTE_APP_NAME(131, "roulette", "自研 转盘 roulette", 0, true, 5, true, true, true),
    SELF_CRASH_APP_NAME(132, "crash", "自研 火箭 crash", 0, true, 5, true, true, true),
    SELF_SUPER_GREEDY(133, "Super Greedy", "自研 Super Greedy", 0, true, 5, true, true, true),

    ADX_TASK(EventCode.ADX_TASK, "Adx task", "adx广告任务", 0),
    TASK_AWARD_DISTRIBUTE(EventCode.TASK, "Task reward", "任务", ItemSceneIdConstant.TASK_AWARD),
    // 2000 ~ 3000 游戏幸运用户占用（活动模板上线后转自动生成）,
    DEFAULT_GAME_LUCKY(2000, "Game Lucky", "游戏幸运用户", 0),


    ADMIN_OPERATOR(9999, "admin operator", "运营操作", 0),

    TIKKO_RECHARGE_REWARD(9998, "tikko recharge send reward", "tikko渠道充值奖励", 0),

    // 10001 ~ 20000 运营活动占用
    SEND_LORD_GIFT_AWARD_ACTIVITY(10001, "Send lord gift award", "送贵族礼物领取奖励活动", ItemSceneIdConstant.SEND_LORD_GIFT_AWARD_ACTIVITY_GAIN),
    PK_ACTIVITY(10002, "PK Activity", "pk活动", 0),
    BEAUTIFUL_HOST_ACTIVITY(10003, "Guarding Your Charming Girl", "最美主播活动", 0),
    HAPPY_HALLOWEEN_ACTIVITY(10004, "Happy Halloween", "万圣节活动", ItemSceneIdConstant.HALLOWEEN_2023_ACTIVITY_GAIN),
    DIWALI_ACTIVITY(EventCode.ACTIVITY_DIWALI, "Happy Diwali Festival", "排灯节活动", ItemSceneIdConstant.DIWALI_ACTIVITY_GAIN, 600000, 800000),
    WEEK_STAR_V2(10006, "Week Star", "Tikko专属周星活动", ItemSceneIdConstant.WEEK_STAR_V2_GAIN),
    MVP_USER_ACTIVITY(10007, "Tikko MVP User Recharge CoinBack Policy is Coming", "MVP 活动", 0),
    XMAS_ACTIVITY(EventCode.ACTIVITY_XMAS, "Merry Christmas", "圣诞节活动2023", ItemSceneIdConstant.XMAS_ACTIVITY_GAIN, 600000, 800000),
    WELFARE_COMMING_ACTIVITY(10009, "Welfare Comming", "福利站活动", ItemSceneIdConstant.WELFARE_COMMING_ACTIVITY_GAIN),
    VALENTINE_S_DAY(10010, "Valentine's Day", "情人节活动", 0),
    HOTTEST_ROOM_PK(10011, "The Hottest Room", "房间pk活动", 0),
    HOLI_ACTIVITY(10012, "Holi Festival", "洒红节2024", ItemSceneIdConstant.HOLI_ACTIVITY_GAIN),
    HAYE_GARMI_ACTIVITY(10013, "Haye Garmi", "夏日派对2024", ItemSceneIdConstant.HAYE_GARMI_ACTIVITY_GAIN),
    GAME_POINTS_PRIZE_DRAW_REWARD(10015, "Game Lucky", "游戏积分抽奖奖励", 0),
    EVENT_SHOOT_ACTIVITY(10016, "Lucky Shot !!!", "射门活动", 0),
    EVENT_SUPER_PLAYER(10017, "Super Gamer", "超级玩家活动2024-06", 0),
    EVENT_RECHARGE_DRAW(10018, "Recharge & dig Treasure", "充值抽奖活动2024-06", 0, 275000, 0),
    EVENT_STAR_CARNIVAL(10019, "Carnival Star", "嘉年华之星", 0, 371250, 0),
    EVENT_LUCKY_GIFT(10020, "Lucky Gift Event", "幸运礼物宣传活动", 0),
    EVENT_SUPER_PLAYER_V2(10021, "Super Gamer", "超级玩家活动2024-07", 0, 233750, 0),
    EVENT_RECHARGE_DRAW_V2(10022, "Recharge & dig Treasure", "充值抽奖活动2024-07", 0, 275000, 0),
    EVENT_STAR_CARNIVAL_V2(10023, "Carnival Star", "嘉年华之星2024-08", 0, 371250, 0),
    INDEPENDENCE_DAY_ACTIVITY_V2(EventCode.EVENT_INDEPENDENCE_DAY_2024_08, "Independence Day", "独立日活动2024-08", 0, 165000, 0),
    EVENT_ANNIVERSARY(EventCode.EVENT_ANNIVERSARY, "Anniversary Celebration", "tikko周年庆", 0),
    EVENT_MISS_ROSE(EventCode.EVENT_MISS_ROSE, "Rose Queen", "玫瑰小姐2024-08", 0, 123750, 0),
    EVENT_CUPID(EventCode.EVENT_CUPID, "Cupid's Arrow", "丘比特之箭2024-08", 0, 261250, 0),
    EVENT_WELFARE_2409(EventCode.EVENT_WELFARE_2409, "Welfare", "福利大乱斗2409", 0, 210349, 0),
    EVENT_RECHARGE_DRAW_202409(EventCode.EVNET_RECHARGE_DRAW_202409, "Recharge & dig Treasure", "充值抽奖活动2024-09", 0, 550000, 0),
    EVENT_PLANT_DATE_PALM(EventCode.EVENT_PLANT_DATE_PALM, "Plant Date Palm", "枣椰树活动(菲律宾)", 0, 206800, 0),
    EVENT_PLANT_DATE_PALM2(EventCode.EVENT_PLANT_DATE_PALM_2, "Plant Date Palm", "枣椰树活动(巴基斯坦)", 0, 206800, 0),
    EVENT_SHOOT_2410(EventCode.EVENT_SHOOT_2410, "Shot together", "Shoot 2410", 0, 206800, 0),
    EVENT_DIWALI_2410(EventCode.EVENT_DIWALI_2410, "Happy Diwali Festival", "排灯节活动2410", 0, 538500, 0),
    EVENT_HALLOWEEN_2410(EventCode.EVENT_HALLOWEEN_2410, "Happy Halloween", "万圣节2410", 0, 210349, 0),
    EVENT_PLANT_DATE_PALM_2410(EventCode.EVENT_PLANT_DATE_PALM_2410, "Plant Date Palm", "枣椰树活动", 0, 189345, 0),
    EVENT_RANK_PK_2411(EventCode.EVENT_RANK_PK_2411, "Party King", "party大王2411", 0, 573750, 0),
    EVENT_HORSE_RACE_2411(EventCode.EVENT_HORSE_RACE_2411, "Horse Racing Challenge", "赛马活动2411", 0, 774928, 0),
    EVENT_STAR_CARNIVAL_V3(EventCode.EVENT_STAR_CARNIVAL_V3, "Sheep Catching Battle", "嘉年华之星2024-12", 0, 105200, 0),
    EVENT_RANK_PK_2412(EventCode.EVENT_RANK_PK_2412, "Party Queen", "女房主争霸赛", 0, 573750, 0),
    EVENT_GALA_ROOM_2412(EventCode.EVENT_GALA_ROOM_2412, "Gala Room Plan", "庆典房2412", 0, 703950, 0, DurationTypeConstant.CURR_MONTH),
    EVENT_GAME_MASTER_2501(EventCode.EVENT_GAME_MASTER_2501, "Game Master", "游戏大师", 0, 325000, 0),
    EVENT_RANK_PK_2501(EventCode.EVENT_RANK_PK_2501, "Party Room Owner Competition", "主播争霸赛", 0, 2325000, 0),
    EVENT_LOVER_2501(EventCode.EVENT_LOVER_2501, "Valentine''s Day", "情人节活动2501", 0, 1000000, 0),
    EVENT_DRAGON_TRAINING_2502(EventCode.EVENT_DRAGON_TRAINING_2502, "dragon training", "组队驯龙2502", 0, 880000, 2000),
    EVENT_PK_2502(EventCode.EVENT_PK_2502, "pk", "直播pk活动2502", 0, 350000, 0),
    EVENT_HOLI_2503(EventCode.EVENT_HOLI_2503, "holi", "洒红节活动2503", 0, 450000, 5000),
    EVENT_RANK_PK_2503(EventCode.EVENT_RANK_PK_2503, "Party Room Owner Competition", "主播争霸赛2503", 0, 673760, 0),
    EVENT_GAME_MASTER_2504(EventCode.EVENT_GAME_MASTER_2504, "Game Master", "游戏大师2504", 0, 625000, 500000),
    EVENT_PK_2504(EventCode.EVENT_PK_2504, "Anchor PK Competition", "直播pk活动2504", 0, 350000, 350000),
    EVENT_RANK_PK_2504(EventCode.EVENT_RANK_PK_2504, "Party Room Owner Competition", "主播争霸赛2504", 0, 673760, 0),
    EVENT_STAR_CARNIVAL_2504(EventCode.EVENT_STAR_CARNIVAL_2504, "Sheep Catching Battle", "嘉年华之星2504", 0, 302500, 0),
    EVENT_RANK_PK_2505(EventCode.EVENT_RANK_PK_2505, "Party Room Owner Competition", "主播争霸赛2505", 0, 1400000, 0),
    EVENT_HORSE_RACE_2505(EventCode.EVENT_HORSE_RACE_2505, "Summer Horse Racing", "赛马活动2505", 0, 774928, 0),


    EVENT_XMAS_2024(EventCode.EVENT_XMAS_2024, "2025,What is your Lucky", "圣诞节活动2024-12", 0, 1721250, 0),
    EVENT_SHOOT_2501(EventCode.EVENT_SHOOT_2501, "Shot together", "Shoot 2501", 0, 636275, 0),
    EVENT_SPEED_2501(EventCode.EVENT_SPEED_2501, "Luxury car club", "极速挑战 2501", 0, 45000, 0),
    RECHARGE_DRAW_2502(EventCode.RECHARGE_DRAW_2502, "Recharge & dig Treasure", "充值抽奖活动2502", 0, 550000, 0),
    RECHARGE_DRAW_2503(EventCode.RECHARGE_DRAW_2503, "Recharge & dig Treasure", "充值抽奖活动2503", 0, 371250, 5000),
    EVENT_SPEED_2503(EventCode.EVENT_SPEED_2503, "Luxury Moto club", "竞技摩托 2503", 0, 50001, 50001),
    EVENT_FOOL_2503(EventCode.EVENT_FOOL_2503, "Happy April Fools' Day!", "愚人节2503", 0, 50001, 50001),
    EVENT_CARD_DRAW_2504(EventCode.EVENT_CARD_DRAW_2504, "Online Recharge & Win Big", "充值抽卡活动2504", 0, 412500, 412500),
    EVENT_IPHONE_2504(EventCode.EVENT_IPHONE_2504, "Send your lover an Iphone", "苹果端手机活动2504", 0, 0, 0),
    EVENT_FUSION_GIFT_2504(EventCode.EVENT_FUSION_GIFT_2504, "Avatar Gift", "融合礼物宣传活动2504", 0, 75000, 75000),
    EVENT_SPEED_2505(EventCode.EVENT_SPEED_2505, "Luxury car club", "极速赛车 2505", 0, 50000, 50000),
    EVENT_RECHARGE_NODE_TASK_2505(EventCode.EVENT_RECHARGE_NODE_TASK_2505, "Online Recharge Bonus Event", "充值节点任务活动2505", 0 , 450000, 2500),
    EVENT_IPHONE_2505(EventCode.EVENT_IPHONE_2505, "Send your lover an Iphone", "苹果端手机活动2505", 0, 0, 0),
    RECHARGE_DRAW_2506(EventCode.EVENT_RECHARGE_DRAW_2506, "Recharge & dig Treasure", "充值抽奖活动2506", 0, 371250, 371250),
    EVENT_MAGIC_LAMP_2506(EventCode.EVENT_MAGIC_LAMP_2506, "Magic Lamp", "神灯挑战2506", 0, 0, 0),

    // 20000 ~ all 活动模板占用（mysql自动递增,将在运营平台进行事件码生成）
    ;


    private final int code;

    private final String name;

    private final String desc;


    // 以下为奖励发放相关配置字段
//    /**
//     *  actType待处理
//     */
//    private final int actType;
    /**
     * a     * 物品场景id
     */
    private final int itemSceneId;

    //以下为im推送相关字段
    /**
     * true是游戏 false不是游戏
     */
    private final boolean isGame;
    /**
     * 推送延时 毫秒级
     */
    private final int pushDelayDuration;
    /**
     * 需要发送房间im
     */
    private final boolean needSendRoomIm;
    /**
     * 需要发送首页轮播im
     */
    private final boolean needSendBroadcastMessageIm;
    /**
     * 新获奖全频道消息 @nanfeng
     */
    private final boolean needSendFull;
    /**
     * 成本限制预警 1
     */
    private final long maxCostLimitOne;
    /**
     * 成本限制预警 2
     */
    private final long maxCostLimitTwo;

    /**
     * 房间资源佩戴
     */
    private final boolean roomItemWear;
    /**
     * 成本限制预警 持续时间类型
     * @see DurationTypeConstant 只支持 CURR_MONTH， CURR_WEEK, TODAY
     */
    private final String costLimitDurationType;

    ActivityTypeEnum(int code, String name, String desc, int itemSceneId, long maxCostLimitOne, long maxCostLimitTwo, String costLimitDurationType) {
        this.code = code;
        this.name = name;
        this.desc = desc;
        this.itemSceneId = itemSceneId;
        this.isGame = false;
        this.pushDelayDuration = 5;
        this.needSendRoomIm = false;
        this.needSendBroadcastMessageIm = false;
        this.needSendFull = false;
        this.maxCostLimitOne = maxCostLimitOne;
        this.maxCostLimitTwo = maxCostLimitTwo;
        this.roomItemWear = false;
        this.costLimitDurationType = costLimitDurationType;
    }

    ActivityTypeEnum(int code, String name, String desc, int itemSceneId, long maxCostLimitOne, long maxCostLimitTwo) {
        this.code = code;
        this.name = name;
        this.desc = desc;
        this.itemSceneId = itemSceneId;
        this.isGame = false;
        this.pushDelayDuration = 5;
        this.needSendRoomIm = false;
        this.needSendBroadcastMessageIm = false;
        this.needSendFull = false;
        this.maxCostLimitOne = maxCostLimitOne;
        this.maxCostLimitTwo = maxCostLimitTwo;
        this.roomItemWear = false;
        this.costLimitDurationType = "";
    }

    ActivityTypeEnum(int code, String name, String desc, int itemSceneId) {
        this.code = code;
        this.name = name;
        this.desc = desc;
        this.itemSceneId = itemSceneId;
        this.isGame = false;
        this.pushDelayDuration = 5;
        this.needSendRoomIm = false;
        this.needSendBroadcastMessageIm = false;
        this.needSendFull = false;
        this.maxCostLimitOne = 0L;
        this.maxCostLimitTwo = 0L;
        this.roomItemWear = false;
        this.costLimitDurationType = "";
    }

    ActivityTypeEnum(int code, String name, String desc, int itemSceneId, boolean isGame) {
        this.code = code;
        this.name = name;
        this.desc = desc;
        this.itemSceneId = itemSceneId;
        this.isGame = isGame;
        this.pushDelayDuration = 5;
        this.needSendRoomIm = false;
        this.needSendBroadcastMessageIm = false;
        this.needSendFull = false;
        this.maxCostLimitOne = 0L;
        this.maxCostLimitTwo = 0L;
        this.roomItemWear = false;
        this.costLimitDurationType = "";
    }

    ActivityTypeEnum(int code, String name, String desc, int itemSceneId, boolean isGame, int pushDelayDuration) {
        this.code = code;
        this.name = name;
        this.desc = desc;
        this.itemSceneId = itemSceneId;
        this.isGame = isGame;
        this.pushDelayDuration = pushDelayDuration;
        this.needSendRoomIm = false;
        this.needSendBroadcastMessageIm = false;
        this.needSendFull = false;
        this.maxCostLimitOne = 0L;
        this.maxCostLimitTwo = 0L;
        this.roomItemWear = false;
        this.costLimitDurationType = "";
    }

    ActivityTypeEnum(int code, String name, String desc, int itemSceneId, boolean isGame, int pushDelayDuration, boolean needSendRoomIm, boolean needSendBroadcastMessageIm, boolean needSendFull) {
        this.code = code;
        this.name = name;
        this.desc = desc;
        this.itemSceneId = itemSceneId;
        this.isGame = isGame;
        this.pushDelayDuration = pushDelayDuration;
        this.needSendRoomIm = needSendRoomIm;
        this.needSendBroadcastMessageIm = needSendBroadcastMessageIm;
        this.needSendFull = needSendFull;
        this.maxCostLimitOne = 0L;
        this.maxCostLimitTwo = 0L;
        this.roomItemWear = false;
        this.costLimitDurationType = "";
    }


    ActivityTypeEnum(int code, String name, String desc, int itemSceneId, boolean isGame, int pushDelayDuration, boolean needSendRoomIm, boolean needSendBroadcastMessageIm, boolean needSendFull, boolean roomItemWear) {
        this.code = code;
        this.name = name;
        this.desc = desc;
        this.itemSceneId = itemSceneId;
        this.isGame = isGame;
        this.pushDelayDuration = pushDelayDuration;
        this.needSendRoomIm = needSendRoomIm;
        this.needSendBroadcastMessageIm = needSendBroadcastMessageIm;
        this.needSendFull = needSendFull;
        this.maxCostLimitOne = 0L;
        this.maxCostLimitTwo = 0L;
        this.roomItemWear = roomItemWear;
        this.costLimitDurationType = "";
    }

    public static ActivityTypeEnum getByCode(int code) {
        for (ActivityTypeEnum eum : ActivityTypeEnum.values()) {
            if (code == eum.getCode()) {
                return eum;
            }
        }
        if (code > 2000 && code < 5000) {
            return DEFAULT_GAME_LUCKY;
        }
        return DEFAULT;
    }

    public static boolean isSendFullServiceMessageActivityType(int activityType) {
        ActivityTypeEnum activityTypeEnum = getByCode(activityType);
        return activityTypeEnum.needSendFull;
    }

    public static boolean isRoomItemWear(int activityType) {
        ActivityTypeEnum byCode = getByCode(activityType);
        return byCode.roomItemWear;
    }


}
